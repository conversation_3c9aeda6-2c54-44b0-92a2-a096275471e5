Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON> nastavení
Config.Debug = true

-- Na<PERSON><PERSON><PERSON> vykrád<PERSON>í
Config.Robbery = {
    -- <PERSON><PERSON><PERSON> č<PERSON>, kterou lze ukrást z kasy
    totalAmount = 10000,

    -- Minimální a maximální částka za jeden "tick"
    minTickAmount = 50,
    maxTickAmount = 200,

    -- <PERSON><PERSON> j<PERSON> "ticky" (ms)
    tickInterval = 1000,

    -- <PERSON><PERSON><PERSON> doba vykrádání (ms)
    robberyDuration = 15000,

    -- Cooldown pro jednotlivé kasy (minuty)
    -- Ka<PERSON>d<PERSON> kasa má 30min cooldown po vykrádání
    registerCooldownTime = 30,

    -- <PERSON>it pro hráče (minuty a počet)
    -- Hr<PERSON><PERSON> může vykrást max 2 kasy za 30 minut
    -- Po vykrádání 2 kas musí čekat 30 minut od nejstarší
    playerCooldownTime = 30, -- minuty od nejstar<PERSON><PERSON> krá<PERSON><PERSON>e
    maxRobberiesPerCooldown = 2, -- max počet kas za cooldown období

    -- <PERSON><PERSON><PERSON><PERSON>, kter<PERSON> jsou potřeba pro vykrádání
    requiredWeapons = {
        'WEAPON_PISTOL',
        'WEAPON_COMBATPISTOL',
        'WEAPON_APPISTOL',
        'WEAPON_PISTOL50',
        'WEAPON_SNSPISTOL',
        'WEAPON_HEAVYPISTOL',
        'WEAPON_VINTAGEPISTOL',
        'WEAPON_MARKSMANPISTOL',
        'WEAPON_REVOLVER',
        'WEAPON_DOUBLEACTION',
        'WEAPON_CERAMICPISTOL',
        'WEAPON_NAVYREVOLVER',
        'WEAPON_GADGETPISTOL'
    }
}

-- Prop modely kas pro targeting
Config.CashRegisterProps = {
    'prop_till_01',
    'prop_till_02',
    'prop_till_03',
    'v_ret_gc_till',
    'prop_cash_register_01',
    'prop_cash_register_02'
}

-- Tracking vykradených kas (server-side)
Config.RobbedRegisters = {}

-- Animace
Config.Animations = {
    robbery = {
        dict = "missheist_jewel",
        anim = "smash_case",
        flag = 16
    },
    money = {
        dict = "mp_common",
        anim = "givetake1_a",
        flag = 48
    }
}

-- Prop peněz
Config.MoneyProp = {
    model = "prop_anim_cash_pile_01",
    bone = 28422, -- right hand
    offset = vector3(0.0, 0.0, 0.0),
    rotation = vector3(0.0, 0.0, 0.0)
}

-- Dispatch nastavení
Config.Dispatch = {
    enabled = true,
    dispatchCode = "10-13", -- Kód pro vykrádání
    blipTime = 60000, -- Jak dlouho bude blip na mapě (ms)
    alertRadius = 100.0 -- Radius pro alert
}
