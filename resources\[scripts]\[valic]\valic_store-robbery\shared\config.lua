Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON> nastavení
Config.Debug = true

-- Na<PERSON><PERSON><PERSON> vykrá<PERSON><PERSON>
Config.Robbery = {
    -- <PERSON><PERSON><PERSON> č<PERSON>tka, kterou lze ukrást z kasy
    totalAmount = 10000,
    
    -- Minimální a maximální částka za jeden "tick"
    minTickAmount = 50,
    maxTickAmount = 200,
    
    -- <PERSON><PERSON> me<PERSON> j<PERSON> "ticky" (ms)
    tickInterval = 1000,
    
    -- <PERSON><PERSON><PERSON> doba vykrád<PERSON>í (ms)
    robberyDuration = 15000,
    
    -- Cooldown mezi vykrádáními stejn<PERSON> ka<PERSON> (minuty)
    cooldownTime = 30,
    
    -- <PERSON><PERSON><PERSON><PERSON>, které jsou potřeba pro vykrádání
    requiredWeapons = {
        'WEAPON_PISTOL',
        'WEAPON_COMBATPISTOL',
        'WEAPON_APPISTOL',
        'WEAPON_PISTOL50',
        'WEAPON_SNSPISTOL',
        'WEAPON_HEAVYPISTOL',
        'WEAPON_VINTAGEPISTOL',
        'WEAPON_MARKSMANPISTOL',
        'WEAPON_REVOLVER',
        'WEAPON_DOUBLEACTION',
        'WEAPON_CERAMICPISTOL',
        'WEAPON_NAVYREVOLVER',
        'WEAPON_GADGETPISTOL'
    }
}

-- Pozice kas v obchodech
Config.CashRegisters = {
    -- 24/7 Stores
    {coords = vector3(25.7, -1347.3, 29.49), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-3038.71, 585.9, 7.9), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-3241.927, 1001.462, 12.83), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(1728.66, 6414.16, 35.03), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(1697.99, 4924.4, 42.06), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(1961.48, 3739.96, 32.34), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(547.79, 2671.79, 42.15), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(2679.25, 3280.12, 55.24), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(2557.94, 382.05, 108.62), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(373.55, 325.56, 103.56), heading = 0.0, robbed = false, lastRobbed = 0},
    
    -- LTD Gasoline
    {coords = vector3(-47.24, -1757.514, 29.4), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-707.501, -914.260, 19.21), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-1820.523, 792.518, 138.11), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(1163.373, -323.801, 69.20), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-2967.79, 390.910, 15.04), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(1166.024, 2708.930, 38.15), heading = 0.0, robbed = false, lastRobbed = 0},
    
    -- Rob's Liquor
    {coords = vector3(1135.808, -982.281, 46.41), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-1222.915, -906.983, 12.32), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-1487.553, -379.107, 40.16), heading = 0.0, robbed = false, lastRobbed = 0},
    {coords = vector3(-2968.243, 390.910, 15.04), heading = 0.0, robbed = false, lastRobbed = 0}
}

-- Animace
Config.Animations = {
    robbery = {
        dict = "missheist_jewel",
        anim = "smash_case",
        flag = 16
    }
}

-- Dispatch nastavení
Config.Dispatch = {
    enabled = true,
    dispatchCode = "10-90", -- Kód pro vykrádání
    blipTime = 60000, -- Jak dlouho bude blip na mapě (ms)
    alertRadius = 100.0 -- Radius pro alert
}
