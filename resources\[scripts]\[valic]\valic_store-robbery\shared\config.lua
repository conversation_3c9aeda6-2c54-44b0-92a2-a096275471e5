Config = {}

-- <PERSON><PERSON><PERSON><PERSON><PERSON> nastavení
Config.Debug = true

-- Na<PERSON><PERSON><PERSON> vykr<PERSON><PERSON><PERSON>
Config.Robbery = {
    -- <PERSON><PERSON><PERSON> č<PERSON>tka, kterou lze ukrást z kasy
    totalAmount = 10000,
    
    -- Minimální a maximální částka za jeden "tick"
    minTickAmount = 50,
    maxTickAmount = 200,
    
    -- <PERSON><PERSON> "ticky" (ms)
    tickInterval = 1000,
    
    -- <PERSON><PERSON><PERSON> doba vykrád<PERSON>í (ms)
    robberyDuration = 15000,
    
    -- Cooldown mezi vykrádáními stejn<PERSON> ka<PERSON> (minuty)
    cooldownTime = 30,
    
    -- <PERSON><PERSON><PERSON><PERSON>, které jsou potřeba pro vykrádání
    requiredWeapons = {
        'WEAPON_PISTOL',
        'WEAPON_COMBATPISTOL',
        'WEAPON_APPISTOL',
        'WEAPON_PISTOL50',
        'WEAPON_SNSPISTOL',
        'WEAPON_HEAVYPISTOL',
        'WEAPON_VINTAGEPISTOL',
        'WEAPON_MARKSMANPISTOL',
        'WEAPON_REVOLVER',
        'WEAPON_DOUBLEACTION',
        'WEAPON_CERAMICPISTOL',
        'WEAPON_NAVYREVOLVER',
        'WEAPON_GADGETPISTOL'
    }
}

-- Prop modely kas pro targeting
Config.CashRegisterProps = {
    'prop_till_01',
    'prop_till_02',
    'prop_till_03',
    'v_ret_gc_till',
    'prop_cash_register_01',
    'prop_cash_register_02'
}

-- Tracking vykradených kas (server-side)
Config.RobbedRegisters = {}

-- Animace
Config.Animations = {
    robbery = {
        dict = "missheist_jewel",
        anim = "smash_case",
        flag = 16
    }
}

-- Dispatch nastavení
Config.Dispatch = {
    enabled = true,
    dispatchCode = "10-13", -- Kód pro vykrádání
    blipTime = 60000, -- Jak dlouho bude blip na mapě (ms)
    alertRadius = 100.0 -- Radius pro alert
}
