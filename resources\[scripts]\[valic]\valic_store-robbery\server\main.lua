local robbedRegisters = {}

-- Funkce pro kontrolu cooldownu
local function isOnCooldown(registerId)
    if not robbedRegisters[registerId] then
        return false
    end
    
    local timePassed = (os.time() - robbedRegisters[registerId]) / 60 -- v minutách
    return timePassed < Config.Robbery.cooldownTime
end

-- Funkce pro získání zbývajícího času cooldownu
local function getCooldownTime(registerId)
    if not robbedRegisters[registerId] then
        return 0
    end
    
    local timePassed = (os.time() - robbedRegisters[registerId]) / 60
    return math.max(0, Config.Robbery.cooldownTime - timePassed)
end

-- Funkce pro odeslání alertu policii
local function sendPoliceAlert(coords, playerId)
    if not Config.Dispatch.enabled then
        return
    end

    -- Opravený cd_dispatch bez GetPlayerInfo
    TriggerEvent('cd_dispatch:AddNotification', {
        job_table = {'police'},
        coords = coords,
        title = Config.Dispatch.dispatchCode .. ' - Vykrádání obchodu',
        message = 'Hl<PERSON><PERSON><PERSON> vykrádání obchodu v oblasti',
        flash = 0,
        unique_id = tostring(math.random(0000000, 9999999)),
        blip = {
            sprite = 458,
            scale = 1.2,
            colour = 1,
            flashes = false,
            text = 'Vykrádání obchodu',
            time = (Config.Dispatch.blipTime / 1000),
            sound = 1,
        }
    })

    if Config.Debug then
        print('^3[STORE ROBBERY]^7 Police alert sent for register at: ' .. coords.x .. ', ' .. coords.y .. ', ' .. coords.z)
    end
end

-- Event pro kontrolu cooldownu
RegisterNetEvent('valic_store-robbery:checkCooldown', function(registerId, coords)
    local src = source

    if isOnCooldown(registerId) then
        local remainingTime = math.ceil(getCooldownTime(registerId))
        TriggerClientEvent('valic_store-robbery:robberyDenied', src,
            'Tato kasa byla nedávno vykradena. Zkus to za ' .. remainingTime .. ' minut.')
        return
    end

    -- Odeslat alert policii
    sendPoliceAlert(coords, src)

    -- Spustit vykrádání
    TriggerClientEvent('valic_store-robbery:startRobbery', src, registerId)

    if Config.Debug then
        print('^3[STORE ROBBERY]^7 Player ' .. src .. ' started robbing register ' .. registerId .. ' at coords: ' .. coords.x .. ', ' .. coords.y .. ', ' .. coords.z)
    end
end)

-- Event pro dokončení vykrádání
RegisterNetEvent('valic_store-robbery:completeRobbery', function(registerId)
    local src = source
    
    -- Nastavit cooldown
    robbedRegisters[registerId] = os.time()
    
    -- Spustit postupné přidávání peněz
    local totalGiven = 0
    local tickCount = math.floor(Config.Robbery.robberyDuration / Config.Robbery.tickInterval)
    
    CreateThread(function()
        for i = 1, tickCount do
            if totalGiven >= Config.Robbery.totalAmount then
                break
            end
            
            local remainingAmount = Config.Robbery.totalAmount - totalGiven
            local tickAmount = math.random(Config.Robbery.minTickAmount, Config.Robbery.maxTickAmount)
            
            -- Ujistit se, že nepřekročíme celkovou částku
            if tickAmount > remainingAmount then
                tickAmount = remainingAmount
            end
            
            -- Přidat peníze do inventáře
            local success = exports.ox_inventory:AddItem(src, 'money', tickAmount)
            
            if success then
                totalGiven = totalGiven + tickAmount
                
                if Config.Debug then
                    print('^3[STORE ROBBERY]^7 Added $' .. tickAmount .. ' to player ' .. src .. ' (Total: $' .. totalGiven .. ')')
                end
            else
                if Config.Debug then
                    print('^1[STORE ROBBERY]^7 Failed to add money to player ' .. src)
                end
                break
            end
            
            Wait(Config.Robbery.tickInterval)
        end
        
        if Config.Debug then
            print('^3[STORE ROBBERY]^7 Player ' .. src .. ' completed robbery of register ' .. registerId .. ' for total $' .. totalGiven)
        end
    end)
end)

-- Event pro zrušení vykrádání
RegisterNetEvent('valic_store-robbery:cancelRobbery', function(registerId)
    local src = source
    
    if Config.Debug then
        print('^3[STORE ROBBERY]^7 Player ' .. src .. ' cancelled robbery of register ' .. registerId)
    end
end)

-- Admin příkaz pro reset cooldownu
RegisterCommand('resetrobbery', function(source, args, rawCommand)
    local src = source

    -- Zde můžeš přidat kontrolu admin práv
    -- Například: if not IsPlayerAceAllowed(src, 'command.resetrobbery') then return end

    if args[1] then
        local registerId = args[1]
        if robbedRegisters[registerId] then
            robbedRegisters[registerId] = nil
            TriggerClientEvent('chat:addMessage', src, {
                color = {0, 255, 0},
                multiline = true,
                args = {"SYSTEM", "Cooldown pro kasu #" .. registerId .. " byl resetován."}
            })
        else
            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 0, 0},
                multiline = true,
                args = {"SYSTEM", "Kasa s tímto ID nebyla vykradena nebo neexistuje."}
            })
        end
    else
        robbedRegisters = {}
        TriggerClientEvent('chat:addMessage', src, {
            color = {0, 255, 0},
            multiline = true,
            args = {"SYSTEM", "Všechny cooldowny byly resetovány."}
        })
    end
end, true) -- true = pouze pro adminy

-- Debug příkaz pro zobrazení stavu kas
if Config.Debug then
    RegisterCommand('robberyinfo', function(source, args, rawCommand)
        local src = source

        TriggerClientEvent('chat:addMessage', src, {
            color = {0, 255, 255},
            multiline = true,
            args = {"ROBBERY INFO", "=== Stav vykradených kas ==="}
        })

        local count = 0
        for registerId, timestamp in pairs(robbedRegisters) do
            count = count + 1
            local status = "Dostupná"
            if isOnCooldown(registerId) then
                local remainingTime = math.ceil(getCooldownTime(registerId))
                status = "Cooldown: " .. remainingTime .. " min"
            end

            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Kasa ID #" .. registerId .. ": " .. status}
            })
        end

        if count == 0 then
            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Žádné kasy nejsou na cooldownu"}
            })
        end
    end, false)
end
