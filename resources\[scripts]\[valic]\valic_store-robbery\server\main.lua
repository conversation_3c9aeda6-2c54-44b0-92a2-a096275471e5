local robbedRegisters = {} -- Cooldown pro jednotlivé kasy
local playerRobberies = {} -- Tracking v<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hr<PERSON>
local activeRobberies = {} -- Tracking aktivních vykrádání

-- Funkce pro kontrolu cooldownu kasy
local function isRegisterOnCooldown(registerId)
    if not robbedRegisters[registerId] then
        return false
    end

    local timePassed = (os.time() - robbedRegisters[registerId]) / 60 -- v minutách
    return timePassed < Config.Robbery.registerCooldownTime
end

-- Funkce pro získání zbývajícího času cooldownu kasy
local function getRegisterCooldownTime(registerId)
    if not robbedRegisters[registerId] then
        return 0
    end

    local timePassed = (os.time() - robbedRegisters[registerId]) / 60
    return math.max(0, Config.Robbery.registerCooldownTime - timePassed)
end

-- Funkce pro kontrolu hrá<PERSON>ova cooldownu
local function canPlayerRob(playerId)
    if not playerRobberies[playerId] then
        return true, 0
    end

    local currentTime = os.time()
    local robberies = playerRobberies[playerId]

    -- Vyčistit staré <PERSON> (starší než cooldown)
    local validRobberies = {}
    for _, timestamp in pairs(robberies) do
        local timePassed = (currentTime - timestamp) / 60 -- v minutách
        if timePassed < Config.Robbery.playerCooldownTime then
            table.insert(validRobberies, timestamp)
        end
    end

    playerRobberies[playerId] = validRobberies

    -- Kontrola limitu
    if #validRobberies >= Config.Robbery.maxRobberiesPerCooldown then
        -- Najít nejstarší vykrádání pro výpočet zbývajícího času
        local oldestRobbery = math.min(table.unpack(validRobberies))
        local timeSinceOldest = (currentTime - oldestRobbery) / 60
        local remainingTime = Config.Robbery.playerCooldownTime - timeSinceOldest

        return false, math.ceil(remainingTime)
    end

    return true, 0
end

-- Funkce pro přidání vykrádání do historie hráče
local function addPlayerRobbery(playerId)
    if not playerRobberies[playerId] then
        playerRobberies[playerId] = {}
    end

    table.insert(playerRobberies[playerId], os.time())
end

-- Funkce pro odeslání alertu policii
local function sendPoliceAlert(coords, playerId)
    if not Config.Dispatch.enabled then
        return
    end

    -- Opravený cd_dispatch bez GetPlayerInfo
    TriggerEvent('cd_dispatch:AddNotification', {
        job_table = {'police'},
        coords = coords,
        title = Config.Dispatch.dispatchCode .. ' - Vykrádání obchodu',
        message = 'Hlášeno vykrádání obchodu v oblasti',
        flash = 0,
        unique_id = tostring(math.random(0000000, 9999999)),
        blip = {
            sprite = 458,
            scale = 1.2,
            colour = 1,
            flashes = false,
            text = 'Vykrádání obchodu',
            time = (Config.Dispatch.blipTime / 1000),
            sound = 1,
        }
    })

    if Config.Debug then
        print('^3[STORE ROBBERY]^7 Police alert sent for register at: ' .. coords.x .. ', ' .. coords.y .. ', ' .. coords.z)
    end
end

-- Event pro kontrolu cooldownu
RegisterNetEvent('valic_store-robbery:checkCooldown', function(registerId, coords)
    local src = source

    -- Kontrola cooldownu kasy
    if isRegisterOnCooldown(registerId) then
        local remainingTime = math.ceil(getRegisterCooldownTime(registerId))
        TriggerClientEvent('valic_store-robbery:robberyDenied', src,
            'Tato kasa byla nedávno vykradena. Zkus to za ' .. remainingTime .. ' minut.')
        return
    end

    -- Kontrola hráčova limitu
    local canRob, playerCooldown = canPlayerRob(src)
    if not canRob then
        TriggerClientEvent('valic_store-robbery:robberyDenied', src,
            'Už jsi vykradl ' .. Config.Robbery.maxRobberiesPerCooldown .. ' kasy. Zkus to za ' .. playerCooldown .. ' minut.')
        return
    end

    -- Odeslat alert policii
    sendPoliceAlert(coords, src)

    -- Spustit vykrádání (přidání do historie až po dokončení)
    TriggerClientEvent('valic_store-robbery:startRobbery', src, registerId)

    if Config.Debug then
        local robberyCount = #(playerRobberies[src] or {})
        print('^3[STORE ROBBERY]^7 Player ' .. src .. ' started robbing register ' .. registerId .. ' (' .. robberyCount .. '/' .. Config.Robbery.maxRobberiesPerCooldown .. ' robberies)')
    end
end)

-- Event pro spuštění přidávání peněz během progressbaru
RegisterNetEvent('valic_store-robbery:startMoneyTicks', function(registerId)
    local src = source

    -- Označit jako aktivní vykrádání
    activeRobberies[src] = {
        registerId = registerId,
        totalGiven = 0,
        startTime = GetGameTimer()
    }

    -- Spustit postupné přidávání peněz
    CreateThread(function()
        local tickCount = math.floor(Config.Robbery.robberyDuration / Config.Robbery.tickInterval)

        for i = 1, tickCount do
            -- Kontrola, zda vykrádání stále probíhá
            if not activeRobberies[src] then
                if Config.Debug then
                    print('^1[STORE ROBBERY]^7 Robbery cancelled for player ' .. src)
                end
                break
            end

            local totalGiven = activeRobberies[src].totalGiven

            if totalGiven >= Config.Robbery.totalAmount then
                break
            end

            local remainingAmount = Config.Robbery.totalAmount - totalGiven
            local tickAmount = math.random(Config.Robbery.minTickAmount, Config.Robbery.maxTickAmount)

            -- Ujistit se, že nepřekročíme celkovou částku
            if tickAmount > remainingAmount then
                tickAmount = remainingAmount
            end

            -- Přidat peníze do inventáře
            local success = exports.ox_inventory:AddItem(src, 'money', tickAmount)

            if success then
                activeRobberies[src].totalGiven = totalGiven + tickAmount

                -- Spustit animaci peněz na clientu
                TriggerClientEvent('valic_store-robbery:playMoneyAnimation', src, tickAmount)

                if Config.Debug then
                    print('^3[STORE ROBBERY]^7 Added $' .. tickAmount .. ' to player ' .. src .. ' (Total: $' .. activeRobberies[src].totalGiven .. ')')
                end
            else
                if Config.Debug then
                    print('^1[STORE ROBBERY]^7 Failed to add money to player ' .. src)
                end
                break
            end

            Wait(Config.Robbery.tickInterval)
        end
    end)
end)

-- Event pro dokončení vykrádání
RegisterNetEvent('valic_store-robbery:completeRobbery', function(registerId)
    local src = source

    -- Nastavit cooldown pro kasu
    robbedRegisters[registerId] = os.time()

    -- Přidat vykrádání do historie hráče (až po dokončení)
    addPlayerRobbery(src)

    if activeRobberies[src] then
        local totalGiven = activeRobberies[src].totalGiven

        if Config.Debug then
            print('^3[STORE ROBBERY]^7 Player ' .. src .. ' completed robbery of register ' .. registerId .. ' for total $' .. totalGiven)
        end

        -- Vyčistit aktivní vykrádání
        activeRobberies[src] = nil
    end
end)

-- Event pro zrušení vykrádání
RegisterNetEvent('valic_store-robbery:cancelRobbery', function(registerId)
    local src = source

    -- Zastavit přidávání peněz
    if activeRobberies[src] then
        local totalGiven = activeRobberies[src].totalGiven
        activeRobberies[src] = nil

        if Config.Debug then
            print('^3[STORE ROBBERY]^7 Player ' .. src .. ' cancelled robbery of register ' .. registerId .. ' (received $' .. totalGiven .. ')')
        end
    end
end)

-- Admin příkaz pro reset cooldownu
RegisterCommand('resetrobbery', function(source, args, rawCommand)
    local src = source

    -- Zde můžeš přidat kontrolu admin práv
    -- Například: if not IsPlayerAceAllowed(src, 'command.resetrobbery') then return end

    if args[1] then
        if args[1] == 'player' and args[2] then
            -- Reset hráčova cooldownu
            local playerId = tonumber(args[2])
            if playerId and playerRobberies[playerId] then
                playerRobberies[playerId] = nil
                TriggerClientEvent('chat:addMessage', src, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"SYSTEM", "Cooldown pro hráče #" .. playerId .. " byl resetován."}
                })
            else
                TriggerClientEvent('chat:addMessage', src, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"SYSTEM", "Hráč s tímto ID neexistuje nebo nemá cooldown."}
                })
            end
        else
            -- Reset cooldownu kasy
            local registerId = args[1]
            if robbedRegisters[registerId] then
                robbedRegisters[registerId] = nil
                TriggerClientEvent('chat:addMessage', src, {
                    color = {0, 255, 0},
                    multiline = true,
                    args = {"SYSTEM", "Cooldown pro kasu #" .. registerId .. " byl resetován."}
                })
            else
                TriggerClientEvent('chat:addMessage', src, {
                    color = {255, 0, 0},
                    multiline = true,
                    args = {"SYSTEM", "Kasa s tímto ID nebyla vykradena nebo neexistuje."}
                })
            end
        end
    else
        -- Reset všech cooldownů
        robbedRegisters = {}
        playerRobberies = {}
        TriggerClientEvent('chat:addMessage', src, {
            color = {0, 255, 0},
            multiline = true,
            args = {"SYSTEM", "Všechny cooldowny byly resetovány."}
        })
    end
end, true) -- true = pouze pro adminy

-- Debug příkaz pro reset hráčova cooldownu
if Config.Debug then
    RegisterCommand('resetmyrobbery', function(source, args, rawCommand)
        local src = source

        if playerRobberies[src] then
            playerRobberies[src] = nil
            TriggerClientEvent('chat:addMessage', src, {
                color = {0, 255, 0},
                multiline = true,
                args = {"SYSTEM", "Tvůj cooldown byl resetován."}
            })
        else
            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 0},
                multiline = true,
                args = {"SYSTEM", "Nemáš žádný cooldown."}
            })
        end
    end, false)
end

-- Debug příkaz pro zobrazení stavu kas a hráčů
if Config.Debug then
    RegisterCommand('robberyinfo', function(source, args, rawCommand)
        local src = source

        -- Informace o kasách
        TriggerClientEvent('chat:addMessage', src, {
            color = {0, 255, 255},
            multiline = true,
            args = {"ROBBERY INFO", "=== Stav kas ==="}
        })

        local registerCount = 0
        for registerId, timestamp in pairs(robbedRegisters) do
            registerCount = registerCount + 1
            local status = "Dostupná"
            if isRegisterOnCooldown(registerId) then
                local remainingTime = math.ceil(getRegisterCooldownTime(registerId))
                status = "Cooldown: " .. remainingTime .. " min"
            end

            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Kasa ID #" .. registerId .. ": " .. status}
            })
        end

        if registerCount == 0 then
            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Žádné kasy nejsou na cooldownu"}
            })
        end

        -- Informace o hráčích
        TriggerClientEvent('chat:addMessage', src, {
            color = {0, 255, 255},
            multiline = true,
            args = {"ROBBERY INFO", "=== Stav hráčů ==="}
        })

        local playerCount = 0
        for playerId, robberies in pairs(playerRobberies) do
            playerCount = playerCount + 1
            local canRob, cooldown = canPlayerRob(playerId)
            local status = canRob and "Může krást" or ("Cooldown: " .. cooldown .. " min")

            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Hráč #" .. playerId .. ": " .. #robberies .. "/" .. Config.Robbery.maxRobberiesPerCooldown .. " - " .. status}
            })
        end

        if playerCount == 0 then
            TriggerClientEvent('chat:addMessage', src, {
                color = {255, 255, 255},
                multiline = true,
                args = {"", "Žádní hráči nemají cooldown"}
            })
        end
    end, false)
end
