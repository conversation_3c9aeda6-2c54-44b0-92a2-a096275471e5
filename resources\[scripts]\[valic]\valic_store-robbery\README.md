# Valic Store Robbery System

## Popis
Kompletní systém pro vykrádání kas v obchodech pro FiveM s podporou ox_target, ox_lib a ox_inventory.

## Funkce
- ✅ **ox_target** interakce s kasami
- ✅ **Animace** rozb<PERSON><PERSON><PERSON><PERSON> kasy
- ✅ **ox_lib progressbar** s možností přerušení
- ✅ **ox_inventory** postupné přidávání peněz
- ✅ **CD dispatch** automatické volání policii
- ✅ **Cooldown systém** pro jednotlivé kasy
- ✅ **Kontrola zbraně** v ruce

## Požadavky
- ox_lib
- ox_target  
- ox_inventory
- cd_dispatch (volitelné)

## Instalace
1. Zkopíruj složku `valic_store-robbery` do `resources/[scripts]/[valic]/`
2. Přidej do `server.cfg`: `ensure valic_store-robbery`
3. Restartuj server

## Konfigurace

### <PERSON>áklad<PERSON><PERSON> nasta<PERSON> (shared/config.lua)
```lua
Config.Robbery = {
    totalAmount = 10000,        -- <PERSON><PERSON>ová částka z kasy
    minTickAmount = 50,         -- Min. částka za tick
    maxTickAmount = 200,        -- Max. částka za tick
    tickInterval = 1000,        -- Interval mezi ticky (ms)
    robberyDuration = 15000,    -- Doba vykrádání (ms)
    cooldownTime = 30,          -- Cooldown mezi vykrádáními (minuty)
}
```

### Pozice kas
V `Config.CashRegisters` můžeš přidat/upravit pozice kas. Každá kasa má:
- `coords` - pozice
- `heading` - rotace
- `robbed` - stav (automaticky spravováno)
- `lastRobbed` - čas posledního vykrádání

## Použití

### Pro hráče
1. Vezmi zbraň do ruky
2. Přijdi ke kase
3. Použij ox_target (pravé tlačítko myši)
4. Vyber "Vykrást kasu"
5. Počkaj na dokončení progressbaru

### Admin příkazy
- `/resetrobbery [ID]` - Reset cooldownu (bez ID = všechny kasy)
- `/robberyinfo` - Zobrazí stav všech kas (pouze v debug módu)
- `/testrobbery` - Test vykrádání první kasy (pouze v debug módu)

## Dispatch integrace

### CD Dispatch
Script je připraven pro cd_dispatch. Automaticky pošle alert policii s:
- Pozicí vykrádání
- Blipem na mapě
- Časovačem

### Jiné dispatch systémy
Uprav funkci `sendPoliceAlert()` v `server/main.lua` podle tvého dispatch systému.

## Customizace

### Animace
Změň animaci v `Config.Animations.robbery`:
```lua
Config.Animations = {
    robbery = {
        dict = "missheist_jewel",
        anim = "smash_case", 
        flag = 16
    }
}
```

### Zbraně
Přidej/odeber povolené zbraně v `Config.Robbery.requiredWeapons`.

### Peníze
Script používá item `money` z ox_inventory. Můžeš změnit na jiný item v `server/main.lua`.

## Debug
Nastav `Config.Debug = true` pro:
- Zobrazení ox_target zón
- Console logy
- Debug příkazy

## Troubleshooting

### Nefunguje ox_target
- Zkontroluj, že máš ox_target správně nainstalovaný
- Zkontroluj pozice kas v configu

### Nechodí progressbar
- Zkontroluj ox_lib instalaci
- Zkontroluj, že máš zbraň v ruce

### Nechodí peníze
- Zkontroluj ox_inventory
- Zkontroluj, že item `money` existuje

## Autor
stepan_valic#0

## Verze
1.0.0
