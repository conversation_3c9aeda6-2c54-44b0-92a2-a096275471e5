# Valic Store Robbery System

## Popis
Kompletní systém pro vykrádání kas v obchodech pro FiveM s podporou ox_target, ox_lib a ox_inventory.

## Funkce
- ✅ **ox_target** interakce s kasami (prop targeting)
- ✅ **Kontinuální animace** po celou dobu vykrádání
- ✅ **ox_lib progressbar** s možností přerušení
- ✅ **ox_inventory** postupné přidávání peněz během progressbaru
- ✅ **CD dispatch** automatické volání policii
- ✅ **Duální cooldown systém**:
  - Ka<PERSON>d<PERSON> kasa má 30min cooldown
  - Hr<PERSON>č může vykrást max 2 kasy za 30 minut
- ✅ **Kontrola zbraně** v ruce
- ✅ **<PERSON>k<PERSON> detekce kas** podle prop modelů

## Požadavky
- ox_lib
- ox_target  
- ox_inventory
- cd_dispatch (volitelné)

## Instalace
1. Zkopíruj složku `valic_store-robbery` do `resources/[scripts]/[valic]/`
2. <PERSON>ř<PERSON>j do `server.cfg`: `ensure valic_store-robbery`
3. Restartuj server

## Konfigurace

### Základní nastavení (shared/config.lua)
```lua
Config.Robbery = {
    totalAmount = 10000,                -- Celková částka z kasy
    minTickAmount = 50,                 -- Min. částka za tick
    maxTickAmount = 200,                -- Max. částka za tick
    tickInterval = 1000,                -- Interval mezi ticky (ms)
    robberyDuration = 15000,            -- Doba vykrádání (ms)
    registerCooldownTime = 30,          -- Cooldown pro kasy (minuty)
    playerCooldownTime = 30,            -- Cooldown pro hráče (minuty)
    maxRobberiesPerCooldown = 2,        -- Max kas za cooldown období
}
```

### Prop modely kas
Script automaticky detekuje tyto modely kas:
- `prop_till_01`, `prop_till_02`, `prop_till_03`
- `v_ret_gc_till`
- `prop_cash_register_01`, `prop_cash_register_02`

Můžeš přidat další modely do `Config.CashRegisterProps`.

## Použití

### Pro hráče
1. Vezmi zbraň do ruky
2. Přijdi ke kase
3. Použij ox_target (pravé tlačítko myši)
4. Vyber "Vykrást kasu"
5. Počkaj na dokončení progressbaru

### Admin příkazy
- `/resetrobbery` - Reset všech cooldownů
- `/resetrobbery [kasaID]` - Reset cooldownu konkrétní kasy
- `/resetrobbery player [hráčID]` - Reset cooldownu konkrétního hráče
- `/robberyinfo` - Zobrazí stav kas a hráčů (pouze v debug módu)
- `/testrobbery` - Test vykrádání nejbližší kasy (pouze v debug módu)

## Dispatch integrace

### CD Dispatch
Script je připraven pro cd_dispatch. Automaticky pošle alert policii s:
- Pozicí vykrádání
- Blipem na mapě
- Časovačem

### Jiné dispatch systémy
Uprav funkci `sendPoliceAlert()` v `server/main.lua` podle tvého dispatch systému.

## Customizace

### Animace
Změň animaci v `Config.Animations.robbery`:
```lua
Config.Animations = {
    robbery = {
        dict = "missheist_jewel",
        anim = "smash_case", 
        flag = 16
    }
}
```

### Zbraně
Přidej/odeber povolené zbraně v `Config.Robbery.requiredWeapons`.

### Peníze
Script používá item `money` z ox_inventory. Můžeš změnit na jiný item v `server/main.lua`.

## Debug
Nastav `Config.Debug = true` pro:
- Zobrazení ox_target zón
- Console logy
- Debug příkazy

## Troubleshooting

### Nefunguje ox_target
- Zkontroluj, že máš ox_target správně nainstalovaný
- Zkontroluj pozice kas v configu

### Nechodí progressbar
- Zkontroluj ox_lib instalaci
- Zkontroluj, že máš zbraň v ruce

### Nechodí peníze
- Zkontroluj ox_inventory
- Zkontroluj, že item `money` existuje

## Autor
stepan_valic#0

## Verze
1.0.0
