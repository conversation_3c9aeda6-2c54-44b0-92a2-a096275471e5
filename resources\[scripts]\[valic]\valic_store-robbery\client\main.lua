local isRobbing = false
local currentRegister = nil

-- Funkce pro kontrolu, zda má hráč zbraň v ruce
local function hasWeaponInHand()
    local ped = PlayerPedId()
    local weapon = GetSelectedPedWeapon(ped)
    
    for _, allowedWeapon in pairs(Config.Robbery.requiredWeapons) do
        if weapon == GetHashKey(allowedWeapon) then
            return true
        end
    end
    return false
end

-- Funkce pro spuštění animace
local function playRobberyAnimation()
    local ped = PlayerPedId()
    
    lib.requestAnimDict(Config.Animations.robbery.dict)
    TaskPlayAnim(ped, Config.Animations.robbery.dict, Config.Animations.robbery.anim, 8.0, -8.0, -1, Config.Animations.robbery.flag, 0, false, false, false)
end

-- Funkce pro zastavení animace
local function stopRobberyAnimation()
    local ped = PlayerPedId()
    ClearPedTasks(ped)
end

-- Funkce pro vykrádání kasy
local function robCashRegister(entity)
    if isRobbing then
        lib.notify({
            title = 'Vykrádání',
            description = '<PERSON>ž něco vykrádáš!',
            type = 'error'
        })
        return
    end

    if not hasWeaponInHand() then
        lib.notify({
            title = 'Vykrádání',
            description = 'Potřebuješ mít zbraň v ruce!',
            type = 'error'
        })
        return
    end

    -- Získat pozici entity pro identifikaci
    local coords = GetEntityCoords(entity)
    local registerId = NetworkGetNetworkIdFromEntity(entity)

    -- Kontrola cooldownu na serveru
    TriggerServerEvent('valic_store-robbery:checkCooldown', registerId, coords)
end

-- Event pro spuštění vykrádání po kontrole cooldownu
RegisterNetEvent('valic_store-robbery:startRobbery', function(registerId)
    isRobbing = true
    currentRegister = registerId
    
    -- Spustit animaci
    playRobberyAnimation()
    
    -- Spustit progressbar
    if lib.progressBar({
        duration = Config.Robbery.robberyDuration,
        label = 'Vykrádáš kasu...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        -- Vykrádání dokončeno úspěšně
        stopRobberyAnimation()
        TriggerServerEvent('valic_store-robbery:completeRobbery', registerId)
        
        lib.notify({
            title = 'Vykrádání',
            description = 'Úspěšně jsi vykradl kasu!',
            type = 'success'
        })
    else
        -- Vykrádání bylo přerušeno
        stopRobberyAnimation()
        TriggerServerEvent('valic_store-robbery:cancelRobbery', registerId)
        
        lib.notify({
            title = 'Vykrádání',
            description = 'Vykrádání bylo přerušeno!',
            type = 'error'
        })
    end
    
    isRobbing = false
    currentRegister = nil
end)

-- Event pro zamítnutí vykrádání (cooldown)
RegisterNetEvent('valic_store-robbery:robberyDenied', function(reason)
    lib.notify({
        title = 'Vykrádání',
        description = reason,
        type = 'error'
    })
end)

-- Inicializace ox_target pro prop kasy
CreateThread(function()
    exports.ox_target:addModel(Config.CashRegisterProps, {
        {
            name = 'rob_cash_register',
            icon = 'fas fa-mask',
            label = 'Vykrást kasu',
            onSelect = function(data)
                robCashRegister(data.entity)
            end,
            canInteract = function(entity, distance, coords, name)
                return hasWeaponInHand() and not isRobbing
            end,
            distance = 2.0
        }
    })
end)

-- Debug příkaz pro testování
if Config.Debug then
    RegisterCommand('testrobbery', function()
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local entity = GetClosestObjectOfType(coords.x, coords.y, coords.z, 5.0, GetHashKey('prop_till_01'), false, false, false)

        if entity and entity ~= 0 then
            robCashRegister(entity)
        else
            lib.notify({
                title = 'Debug',
                description = 'Žádná kasa poblíž nebyla nalezena',
                type = 'error'
            })
        end
    end, false)
end
