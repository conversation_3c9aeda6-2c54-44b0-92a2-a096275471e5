local isRobbing = false
local currentRegister = nil

-- Funkce pro kontrolu, zda má hráč zbraň v ruce
local function hasWeaponInHand()
    local ped = PlayerPedId()
    local weapon = GetSelectedPedWeapon(ped)
    
    for _, allowedWeapon in pairs(Config.Robbery.requiredWeapons) do
        if weapon == GetHashKey(allowedWeapon) then
            return true
        end
    end
    return false
end

-- Funkce pro spuštění animace
local function playRobberyAnimation()
    local ped = PlayerPedId()
    
    lib.requestAnimDict(Config.Animations.robbery.dict)
    TaskPlayAnim(ped, Config.Animations.robbery.dict, Config.Animations.robbery.anim, 8.0, -8.0, -1, Config.Animations.robbery.flag, 0, false, false, false)
end

-- Funkce pro zastavení animace
local function stopRobberyAnimation()
    local ped = PlayerPedId()
    ClearPedTasks(ped)
end

-- <PERSON>ce pro vykrádání kasy
local function robCashRegister(registerId)
    if isRobbing then
        lib.notify({
            title = 'Vykrádání',
            description = 'Už něco vykrádáš!',
            type = 'error'
        })
        return
    end

    if not hasWeaponInHand() then
        lib.notify({
            title = 'Vykrádání',
            description = 'Potřebuješ mít zbraň v ruce!',
            type = 'error'
        })
        return
    end

    -- Kontrola cooldownu na serveru
    TriggerServerEvent('valic_store-robbery:checkCooldown', registerId)
end

-- Event pro spuštění vykrádání po kontrole cooldownu
RegisterNetEvent('valic_store-robbery:startRobbery', function(registerId)
    isRobbing = true
    currentRegister = registerId
    
    -- Spustit animaci
    playRobberyAnimation()
    
    -- Spustit progressbar
    if lib.progressBar({
        duration = Config.Robbery.robberyDuration,
        label = 'Vykrádáš kasu...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        -- Vykrádání dokončeno úspěšně
        stopRobberyAnimation()
        TriggerServerEvent('valic_store-robbery:completeRobbery', registerId)
        
        lib.notify({
            title = 'Vykrádání',
            description = 'Úspěšně jsi vykradl kasu!',
            type = 'success'
        })
    else
        -- Vykrádání bylo přerušeno
        stopRobberyAnimation()
        TriggerServerEvent('valic_store-robbery:cancelRobbery', registerId)
        
        lib.notify({
            title = 'Vykrádání',
            description = 'Vykrádání bylo přerušeno!',
            type = 'error'
        })
    end
    
    isRobbing = false
    currentRegister = nil
end)

-- Event pro zamítnutí vykrádání (cooldown)
RegisterNetEvent('valic_store-robbery:robberyDenied', function(reason)
    lib.notify({
        title = 'Vykrádání',
        description = reason,
        type = 'error'
    })
end)

-- Inicializace ox_target pro všechny kasy
CreateThread(function()
    for i, register in pairs(Config.CashRegisters) do
        exports.ox_target:addBoxZone({
            coords = register.coords,
            size = vec3(1.0, 1.0, 1.0),
            rotation = register.heading,
            debug = Config.Debug,
            options = {
                {
                    name = 'rob_register_' .. i,
                    icon = 'fas fa-mask',
                    label = 'Vykrást kasu',
                    onSelect = function()
                        robCashRegister(i)
                    end,
                    canInteract = function()
                        return hasWeaponInHand() and not isRobbing
                    end
                }
            }
        })
    end
end)

-- Debug příkaz pro testování
if Config.Debug then
    RegisterCommand('testrobbery', function()
        if #Config.CashRegisters > 0 then
            robCashRegister(1)
        end
    end, false)
end
