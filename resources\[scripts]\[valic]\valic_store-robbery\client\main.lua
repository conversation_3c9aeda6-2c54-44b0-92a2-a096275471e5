local isRobbing = false
local currentRegister = nil
local animationThread = nil
local moneyProp = nil

-- Funkce pro kontrolu, zda má hráč zbraň v ruce
local function hasWeaponInHand()
    local ped = PlayerPedId()
    local weapon = GetSelectedPedWeapon(ped)
    
    for _, allowedWeapon in pairs(Config.Robbery.requiredWeapons) do
        if weapon == GetHashKey(allowedWeapon) then
            return true
        end
    end
    return false
end

-- Funkce pro vytvoření prop peněz
local function createMoneyProp()
    local ped = PlayerPedId()
    local model = GetHashKey(Config.MoneyProp.model)

    lib.requestModel(model)

    moneyProp = CreateObject(model, 0.0, 0.0, 0.0, true, true, false)
    AttachEntityToEntity(
        moneyProp, ped,
        GetPedBoneIndex(ped, Config.MoneyProp.bone),
        Config.MoneyProp.offset.x, Config.MoneyProp.offset.y, Config.MoneyProp.offset.z,
        Config.MoneyProp.rotation.x, Config.MoneyProp.rotation.y, Config.MoneyProp.rotation.z,
        false, false, false, false, 2, true
    )
end

-- Funkce pro odstranění prop peněz
local function removeMoneyProp()
    if moneyProp and DoesEntityExist(moneyProp) then
        DeleteEntity(moneyProp)
        moneyProp = nil
    end
end

-- Funkce pro animaci dávání peněz do kapsy
local function playMoneyAnimation()
    local ped = PlayerPedId()

    -- Vytvořit prop peněz
    createMoneyProp()

    -- Přehrát animaci dávání do kapsy
    lib.requestAnimDict(Config.Animations.money.dict)
    TaskPlayAnim(ped, Config.Animations.money.dict, Config.Animations.money.anim, 8.0, -8.0, 2000, Config.Animations.money.flag, 0, false, false, false)

    -- Odstranit prop po animaci
    SetTimeout(2000, function()
        removeMoneyProp()
    end)
end

-- Funkce pro spuštění kontinuální animace
local function startRobberyAnimation()
    local ped = PlayerPedId()

    lib.requestAnimDict(Config.Animations.robbery.dict)

    -- Spustit thread pro kontinuální animaci
    animationThread = CreateThread(function()
        while isRobbing do
            if not IsEntityPlayingAnim(ped, Config.Animations.robbery.dict, Config.Animations.robbery.anim, 3) then
                TaskPlayAnim(ped, Config.Animations.robbery.dict, Config.Animations.robbery.anim, 8.0, -8.0, -1, Config.Animations.robbery.flag, 0, false, false, false)
            end
            Wait(1000) -- Kontrola každou sekundu
        end
    end)
end

-- Funkce pro zastavení animace
local function stopRobberyAnimation()
    local ped = PlayerPedId()

    -- Zastavit animation thread
    if animationThread then
        animationThread = nil
    end

    -- Odstranit prop pokud existuje
    removeMoneyProp()

    ClearPedTasks(ped)
end

-- Funkce pro vykrádání kasy
local function robCashRegister(entity)
    if isRobbing then
        lib.notify({
            title = 'Vykrádání',
            description = 'Už něco vykrádáš!',
            type = 'error'
        })
        return
    end

    if not hasWeaponInHand() then
        lib.notify({
            title = 'Vykrádání',
            description = 'Potřebuješ mít zbraň v ruce!',
            type = 'error'
        })
        return
    end

    -- Získat pozici entity pro identifikaci
    local coords = GetEntityCoords(entity)
    local registerId = NetworkGetNetworkIdFromEntity(entity)

    -- Kontrola cooldownu na serveru
    TriggerServerEvent('valic_store-robbery:checkCooldown', registerId, coords)
end

-- Event pro spuštění vykrádání po kontrole cooldownu
RegisterNetEvent('valic_store-robbery:startRobbery', function(registerId)
    isRobbing = true
    currentRegister = registerId

    -- Spustit kontinuální animaci
    startRobberyAnimation()

    -- Spustit server-side přidávání peněz
    TriggerServerEvent('valic_store-robbery:startMoneyTicks', registerId)

    -- Spustit progressbar
    if lib.progressBar({
        duration = Config.Robbery.robberyDuration,
        label = 'Vykrádáš kasu...',
        useWhileDead = false,
        canCancel = true,
        disable = {
            car = true,
            move = true,
            combat = true
        }
    }) then
        -- Vykrádání dokončeno úspěšně
        isRobbing = false
        stopRobberyAnimation()

        -- Počkat chvilku a pak odstranit prop peněz
        SetTimeout(1000, function()
            removeMoneyProp()
        end)

        TriggerServerEvent('valic_store-robbery:completeRobbery', registerId)

        lib.notify({
            title = 'Vykrádání',
            description = 'Úspěšně jsi vykradl kasu!',
            type = 'success'
        })
    else
        -- Vykrádání bylo přerušeno
        isRobbing = false
        stopRobberyAnimation()
        TriggerServerEvent('valic_store-robbery:cancelRobbery', registerId)

        lib.notify({
            title = 'Vykrádání',
            description = 'Vykrádání bylo přerušeno!',
            type = 'error'
        })
    end

    currentRegister = nil
end)

-- Event pro zamítnutí vykrádání (cooldown)
RegisterNetEvent('valic_store-robbery:robberyDenied', function(reason)
    lib.notify({
        title = 'Vykrádání',
        description = reason,
        type = 'error'
    })
end)

-- Event pro přehrání animace peněz
RegisterNetEvent('valic_store-robbery:playMoneyAnimation', function(amount)
    if isRobbing then
        playMoneyAnimation()

        -- Zobrazit notifikaci o přidaných penězích
        lib.notify({
            title = 'Vykrádání',
            description = 'Získal jsi $' .. amount,
            type = 'success',
            duration = 1500
        })
    end
end)

-- Inicializace ox_target pro prop kasy
CreateThread(function()
    exports.ox_target:addModel(Config.CashRegisterProps, {
        {
            name = 'rob_cash_register',
            icon = 'fas fa-mask',
            label = 'Vykrást kasu',
            onSelect = function(data)
                robCashRegister(data.entity)
            end,
            canInteract = function(entity, distance, coords, name)
                return hasWeaponInHand() and not isRobbing
            end,
            distance = 2.0
        }
    })
end)

-- Debug příkaz pro testování
if Config.Debug then
    RegisterCommand('testrobbery', function()
        local ped = PlayerPedId()
        local coords = GetEntityCoords(ped)
        local entity = GetClosestObjectOfType(coords.x, coords.y, coords.z, 5.0, GetHashKey('prop_till_01'), false, false, false)

        if entity and entity ~= 0 then
            robCashRegister(entity)
        else
            lib.notify({
                title = 'Debug',
                description = 'Žádná kasa poblíž nebyla nalezena',
                type = 'error'
            })
        end
    end, false)
end
